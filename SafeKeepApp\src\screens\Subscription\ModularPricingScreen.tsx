import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip,
  Checkbox,
  Divider,
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CardField, useConfirmPayment, CardFieldInput } from '@stripe/stripe-react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { RootStackParamList } from '../../navigation/types';
import { COLORS, SPACING, FONTS } from '../../utils/constants';

const { width } = Dimensions.get('window');

// Service definitions with pricing and features
const SERVICES = {
  contacts: {
    id: 'contacts',
    name: 'Contacts Backup',
    icon: 'account-multiple',
    price: 99, // $0.99 in cents
    storage: '1GB',
    description: 'Secure backup and sync of your contacts',
    features: [
      'Contact deduplication',
      'Cross-device sync',
      'Backup history',
      'Export options'
    ]
  },
  messages: {
    id: 'messages',
    name: 'Messages Backup',
    icon: 'message-text',
    price: 199, // $1.99 in cents
    storage: '2GB',
    description: 'Secure backup and sync of your messages',
    features: [
      'SMS & MMS backup',
      'Message threading',
      'Search functionality',
      'Media attachments'
    ]
  },
  photos: {
    id: 'photos',
    name: 'Photos Backup',
    icon: 'camera',
    price: 499, // $4.99 in cents
    storage: '10GB',
    description: 'Secure backup and sync of your photos',
    features: [
      'Automatic photo backup',
      'Smart compression',
      'Album organization',
      'Face recognition'
    ]
  }
};

// Combination plans with savings
const COMBINATION_PLANS = {
  'contacts,messages': {
    id: 'contacts-messages',
    name: 'Contacts + Messages',
    price: 249, // $2.49 (save $0.49)
    savings: 49,
    storage: '3GB',
    isPopular: false
  },
  'contacts,photos': {
    id: 'contacts-photos',
    name: 'Contacts + Photos',
    price: 549, // $5.49 (save $0.49)
    savings: 49,
    storage: '11GB',
    isPopular: false
  },
  'messages,photos': {
    id: 'messages-photos',
    name: 'Messages + Photos',
    price: 649, // $6.49 (save $0.49)
    savings: 49,
    storage: '12GB',
    isPopular: true
  },
  'contacts,messages,photos': {
    id: 'complete-backup',
    name: 'Complete Backup',
    price: 699, // $6.99 (save $0.98)
    savings: 98,
    storage: '13GB',
    isPopular: true
  }
};

type ModularPricingScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const ModularPricingScreen = () => {
  const navigation = useNavigation<ModularPricingScreenNavigationProp>();
  const { confirmPayment } = useConfirmPayment();
  
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardFieldInput.Details | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [currentPricing, setCurrentPricing] = useState<{
    totalPrice: number;
    individualTotal: number;
    savings: number;
    recommendedPlan?: any;
    totalStorage: string;
  }>({
    totalPrice: 0,
    individualTotal: 0,
    savings: 0,
    totalStorage: '0GB'
  });

  // Calculate pricing based on selected services
  const calculatePricing = useCallback(() => {
    if (selectedServices.length === 0) {
      setCurrentPricing({
        totalPrice: 0,
        individualTotal: 0,
        savings: 0,
        totalStorage: '0GB'
      });
      return;
    }

    // Calculate individual total
    const individualTotal = selectedServices.reduce((total, serviceId) => {
      return total + SERVICES[serviceId as keyof typeof SERVICES].price;
    }, 0);

    // Check for combination plans
    const sortedServices = [...selectedServices].sort().join(',');
    const combinationPlan = COMBINATION_PLANS[sortedServices as keyof typeof COMBINATION_PLANS];

    let totalPrice = individualTotal;
    let savings = 0;
    let totalStorage = selectedServices.reduce((total, serviceId) => {
      const storage = parseInt(SERVICES[serviceId as keyof typeof SERVICES].storage);
      return total + storage;
    }, 0) + 'GB';

    if (combinationPlan) {
      totalPrice = combinationPlan.price;
      savings = individualTotal - totalPrice;
      totalStorage = combinationPlan.storage;
    }

    setCurrentPricing({
      totalPrice,
      individualTotal,
      savings,
      recommendedPlan: combinationPlan,
      totalStorage
    });
  }, [selectedServices]);

  useEffect(() => {
    calculatePricing();
  }, [calculatePricing]);

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  };

  const handleContinue = () => {
    if (selectedServices.length === 0) {
      Alert.alert('No Services Selected', 'Please select at least one service to continue.');
      return;
    }
    setShowPaymentForm(true);
  };

  const handlePayment = async () => {
    if (!cardDetails?.complete) {
      Alert.alert('Incomplete Card Details', 'Please complete your card information.');
      return;
    }

    setIsProcessing(true);

    try {
      // Create payment intent via backend
      const paymentIntentResponse = await StripeService.createPaymentIntent({
        amount: currentPricing.totalPrice,
        currency: 'usd',
        description: `SafeKeep ${currentPricing.recommendedPlan?.name || 'Custom'} Subscription`,
        serviceIds: selectedServices,
      });

      if (!paymentIntentResponse.client_secret) {
        throw new Error('Failed to create payment intent');
      }

      // Confirm payment with Stripe
      const { error, paymentIntent } = await confirmPayment(
        paymentIntentResponse.client_secret,
        {
          paymentMethodType: 'Card',
          paymentMethodData: {
            billingDetails: {
              email: '<EMAIL>', // In production, get from user profile
            },
          },
        }
      );

      if (error) {
        console.error('Payment confirmation error:', error);
        Alert.alert(
          'Payment Failed',
          error.message || 'There was an error processing your payment. Please try again.'
        );
      } else if (paymentIntent) {
        Alert.alert(
          'Payment Successful!',
          'Your subscription has been activated. Welcome to SafeKeep!',
          [
            {
              text: 'Continue',
              onPress: () => navigation.navigate('Dashboard'),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert(
        'Payment Error',
        'There was an error processing your payment. Please try again.'
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const renderServiceCard = (service: typeof SERVICES[keyof typeof SERVICES]) => {
    const isSelected = selectedServices.includes(service.id);

    return (
      <TouchableOpacity
        key={service.id}
        onPress={() => handleServiceToggle(service.id)}
        style={[
          styles.serviceCard,
          isSelected && styles.selectedServiceCard
        ]}
      >
        <View style={styles.serviceHeader}>
          <View style={styles.serviceIconContainer}>
            <Icon name={service.icon} size={24} color={isSelected ? COLORS.primary : COLORS.textSecondary} />
          </View>
          <View style={styles.serviceInfo}>
            <Text style={[styles.serviceName, isSelected && styles.selectedServiceName]}>
              {service.name}
            </Text>
            <Text style={styles.serviceDescription}>
              {service.description}
            </Text>
          </View>
          <View style={styles.servicePricing}>
            <Text style={[styles.servicePrice, isSelected && styles.selectedServicePrice]}>
              ${(service.price / 100).toFixed(2)}
            </Text>
            <Text style={styles.servicePeriod}>/month</Text>
            <Checkbox
              status={isSelected ? 'checked' : 'unchecked'}
              onPress={() => handleServiceToggle(service.id)}
              color={COLORS.primary}
            />
          </View>
        </View>

        <View style={styles.serviceDetails}>
          <View style={styles.storageInfo}>
            <Icon name="cloud-outline" size={16} color={COLORS.textSecondary} />
            <Text style={styles.storageText}>{service.storage} storage</Text>
          </View>

          <View style={styles.featuresList}>
            {service.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Icon name="check" size={14} color={COLORS.success} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderPricingSummary = () => {
    if (selectedServices.length === 0) {
      return (
        <Card style={styles.pricingSummaryCard}>
          <Card.Content style={styles.pricingSummaryContent}>
            <Text style={styles.pricingSummaryTitle}>Select Services</Text>
            <Text style={styles.pricingSummarySubtitle}>
              Choose the backup services you need and see your pricing automatically calculated
            </Text>
          </Card.Content>
        </Card>
      );
    }

    return (
      <Card style={[styles.pricingSummaryCard, styles.activePricingSummary]}>
        <Card.Content style={styles.pricingSummaryContent}>
          {currentPricing.recommendedPlan && (
            <View style={styles.recommendedPlanHeader}>
              <Chip
                style={[
                  styles.planChip,
                  currentPricing.recommendedPlan.isPopular && styles.popularPlanChip
                ]}
                textStyle={styles.planChipText}
              >
                {currentPricing.recommendedPlan.isPopular ? '🔥 Most Popular' : '💡 Recommended'}
              </Chip>
              <Text style={styles.recommendedPlanName}>
                {currentPricing.recommendedPlan.name}
              </Text>
            </View>
          )}

          <View style={styles.pricingBreakdown}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Total</Text>
              <Text style={styles.totalPrice}>
                ${(currentPricing.totalPrice / 100).toFixed(2)}/month
              </Text>
            </View>

            {currentPricing.savings > 0 && (
              <View style={styles.savingsRow}>
                <Text style={styles.savingsLabel}>You save</Text>
                <Text style={styles.savingsAmount}>
                  ${(currentPricing.savings / 100).toFixed(2)}/month
                </Text>
              </View>
            )}

            <View style={styles.storageRow}>
              <Icon name="cloud-outline" size={16} color={COLORS.textSecondary} />
              <Text style={styles.storageTotal}>{currentPricing.totalStorage} total storage</Text>
            </View>
          </View>

          {currentPricing.savings > 0 && (
            <View style={styles.savingsHighlight}>
              <Icon name="trending-down" size={16} color={COLORS.success} />
              <Text style={styles.savingsText}>
                Save ${(currentPricing.savings / 100).toFixed(2)} compared to individual services
              </Text>
            </View>
          )}

          <View style={styles.selectedServicesList}>
            <Text style={styles.selectedServicesTitle}>Included services:</Text>
            {selectedServices.map(serviceId => (
              <View key={serviceId} style={styles.selectedServiceItem}>
                <Icon name="check-circle" size={14} color={COLORS.success} />
                <Text style={styles.selectedServiceText}>
                  {SERVICES[serviceId as keyof typeof SERVICES].name}
                </Text>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>
    );
  };

  if (showPaymentForm) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          centerComponent={{
            text: 'Complete Payment',
            style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
          }}
          leftComponent={{
            icon: 'arrow-back',
            color: '#fff',
            onPress: () => setShowPaymentForm(false)
          }}
          backgroundColor={COLORS.primary}
        />

        <ScrollView style={styles.content}>
          {/* Selected Plan Summary */}
          <Card style={styles.summaryCard}>
            <Card.Content>
              <Text style={styles.summaryTitle}>
                {currentPricing.recommendedPlan?.name || 'Custom Selection'}
              </Text>
              <Text style={styles.summaryPrice}>
                ${(currentPricing.totalPrice / 100).toFixed(2)}/month
              </Text>
              <Text style={styles.summaryDescription}>
                You're subscribing to {selectedServices.length} service{selectedServices.length > 1 ? 's' : ''} with secure backup and encryption.
              </Text>

              {currentPricing.savings > 0 && (
                <View style={styles.summaryBadge}>
                  <Text style={styles.summaryBadgeText}>
                    💰 Save ${(currentPricing.savings / 100).toFixed(2)}/month
                  </Text>
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Payment Form */}
          <Card style={styles.paymentCard}>
            <Card.Content>
              <Text style={styles.paymentTitle}>Payment Information</Text>

              <CardField
                postalCodeEnabled={true}
                placeholders={{
                  number: '4242 4242 4242 4242',
                }}
                cardStyle={styles.cardField}
                style={styles.cardFieldContainer}
                onCardChange={(cardDetails) => {
                  setCardDetails(cardDetails);
                }}
              />

              {/* Payment Button */}
              <Button
                mode="contained"
                onPress={handlePayment}
                disabled={!cardDetails?.complete || isProcessing}
                style={styles.paymentButton}
                contentStyle={styles.paymentButtonContent}
              >
                {isProcessing ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  `Subscribe for $${(currentPricing.totalPrice / 100).toFixed(2)}/month`
                )}
              </Button>

              <Text style={styles.disclaimer}>
                By subscribing, you agree to SafeKeep's Terms of Service and Privacy Policy.
                You can cancel anytime from your account settings.
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Choose Your Services',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        leftComponent={{
          icon: 'arrow-back',
          color: '#fff',
          onPress: () => navigation.goBack()
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>
            Mix & Match Your Perfect Plan
          </Text>
          <Text style={styles.headerSubtitle}>
            Select only the services you need. Get automatic savings on combinations with military-grade encryption for all your data.
          </Text>
        </View>

        {/* Services Selection */}
        <View style={styles.servicesContainer}>
          <Text style={styles.sectionTitle}>Available Services</Text>
          {Object.values(SERVICES).map(renderServiceCard)}
        </View>

        {/* Pricing Summary */}
        <View style={styles.pricingSummaryContainer}>
          {renderPricingSummary()}
        </View>

        {/* Continue Button */}
        {selectedServices.length > 0 && (
          <Button
            mode="contained"
            onPress={handleContinue}
            style={styles.continueButton}
            contentStyle={styles.continueButtonContent}
          >
            Continue with {selectedServices.length} service{selectedServices.length > 1 ? 's' : ''}
          </Button>
        )}

        {/* Security Notice */}
        <Card style={styles.securityCard}>
          <Card.Content>
            <View style={styles.securityHeader}>
              <Icon name="shield-check" size={24} color={COLORS.success} />
              <Text style={styles.securityTitle}>Your Data is Secure</Text>
            </View>
            <Text style={styles.securityDescription}>
              All services include end-to-end AES-256 encryption, zero-knowledge architecture, and GDPR compliance. Your data is encrypted before it leaves your device.
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  headerTitle: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
  },
  servicesContainer: {
    marginBottom: SPACING.xl,
  },
  serviceCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    borderWidth: 2,
    borderColor: COLORS.border,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  selectedServiceCard: {
    borderColor: COLORS.primary,
    backgroundColor: 'rgba(74, 144, 226, 0.05)',
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  serviceIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  selectedServiceName: {
    color: COLORS.primary,
  },
  serviceDescription: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  servicePricing: {
    alignItems: 'flex-end',
  },
  servicePrice: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  selectedServicePrice: {
    color: COLORS.primary,
  },
  servicePeriod: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  serviceDetails: {
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  storageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  storageText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  featuresList: {
    gap: SPACING.xs,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
    flex: 1,
  },
  pricingSummaryContainer: {
    marginBottom: SPACING.xl,
  },
  pricingSummaryCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  activePricingSummary: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  pricingSummaryContent: {
    padding: SPACING.lg,
  },
  pricingSummaryTitle: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  pricingSummarySubtitle: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  recommendedPlanHeader: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  planChip: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  popularPlanChip: {
    backgroundColor: COLORS.accent,
  },
  planChipText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  recommendedPlanName: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    textAlign: 'center',
  },
  pricingBreakdown: {
    marginBottom: SPACING.lg,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  priceLabel: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
  },
  totalPrice: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  savingsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  savingsLabel: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.success,
  },
  savingsAmount: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.success,
  },
  storageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.sm,
  },
  storageTotal: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  savingsHighlight: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: SPACING.sm,
    borderRadius: 8,
    marginBottom: SPACING.lg,
  },
  savingsText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.success,
    marginLeft: SPACING.xs,
    fontWeight: '600',
  },
  selectedServicesList: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SPACING.md,
  },
  selectedServicesTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  selectedServiceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  selectedServiceText: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    marginBottom: SPACING.xl,
  },
  continueButtonContent: {
    paddingVertical: SPACING.sm,
  },
  securityCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
    borderRadius: 12,
    marginBottom: SPACING.xl,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  securityTitle: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.success,
    marginLeft: SPACING.sm,
  },
  securityDescription: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  // Payment form styles
  summaryCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    marginBottom: SPACING.lg,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  summaryPrice: {
    fontSize: FONTS.sizes.xlarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  summaryDescription: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: SPACING.sm,
  },
  summaryBadge: {
    backgroundColor: COLORS.success,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  summaryBadgeText: {
    color: '#FFFFFF',
    fontSize: FONTS.sizes.small,
    fontWeight: '600',
  },
  paymentCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    marginBottom: SPACING.lg,
  },
  paymentTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.lg,
  },
  cardFieldContainer: {
    height: 50,
    marginBottom: SPACING.lg,
  },
  cardField: {
    backgroundColor: '#FFFFFF',
    textColor: COLORS.text,
    borderColor: COLORS.border,
    borderWidth: 1,
    borderRadius: 8,
  },
  paymentButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    marginBottom: SPACING.lg,
  },
  paymentButtonContent: {
    paddingVertical: SPACING.sm,
  },
  disclaimer: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default ModularPricingScreen;
